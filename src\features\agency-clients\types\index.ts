/**
 * Agency Client Management Types
 */

export interface AgencyClient {
  id: string;
  agency_entity_id: string;
  name: string;
  type: 'company' | 'person';
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  vat_number?: string | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
}

export interface AgencyClientContact {
  id: string;
  client_id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
  position?: string | null;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgencyClientWithContacts extends AgencyClient {
  contacts?: AgencyClientContact[];
}

export interface CreateAgencyClientData {
  agency_entity_id: string;
  name: string;
  type: 'company' | 'person';
  email?: string;
  phone?: string;
  address?: string;
  vat_number?: string;
  notes?: string;
  contacts?: CreateAgencyClientContactData[];
}

export interface CreateAgencyClientContactData {
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  is_primary?: boolean;
}

export interface EditableContactData extends CreateAgencyClientContactData {
  id?: string; // Existing contacts have IDs, new ones don't
  isNew?: boolean; // Flag to track if this is a new contact
  isDeleted?: boolean; // Flag to track if this contact should be deleted
}

export interface UpdateAgencyClientData {
  name?: string;
  type?: 'company' | 'person';
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  vat_number?: string | null;
  notes?: string | null;
}

export interface UpdateAgencyClientContactData {
  name?: string;
  email?: string | null;
  phone?: string | null;
  position?: string | null;
  is_primary?: boolean;
}

export interface ClientFilters {
  search?: string;
  type?: 'company' | 'person' | 'all';
}

export interface ClientListResponse {
  clients: AgencyClientWithContacts[];
  total: number;
  page: number;
  limit: number;
}
