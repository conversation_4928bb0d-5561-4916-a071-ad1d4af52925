-- Agency Client Management Migration
-- Run this SQL in your Supabase SQL Editor

-- Create agency_clients table
CREATE TABLE IF NOT EXISTS public.agency_clients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agency_entity_id UUID NOT NULL REFERENCES public.entities(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('company', 'person')),
    email TEXT,
    phone TEXT,
    address TEXT,
    vat_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create agency_client_contacts table
CREATE TABLE IF NOT EXISTS public.agency_client_contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.agency_clients(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    position TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_agency_clients_agency_entity_id ON public.agency_clients(agency_entity_id);
CREATE INDEX IF NOT EXISTS idx_agency_clients_type ON public.agency_clients(type);
CREATE INDEX IF NOT EXISTS idx_agency_clients_name ON public.agency_clients(name);
CREATE INDEX IF NOT EXISTS idx_agency_client_contacts_client_id ON public.agency_client_contacts(client_id);
CREATE INDEX IF NOT EXISTS idx_agency_client_contacts_is_primary ON public.agency_client_contacts(is_primary);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_agency_clients_updated_at 
    BEFORE UPDATE ON public.agency_clients 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agency_client_contacts_updated_at 
    BEFORE UPDATE ON public.agency_client_contacts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.agency_clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_client_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for agency_clients
CREATE POLICY "Users can view clients for their agencies" ON public.agency_clients
    FOR SELECT USING (
        agency_entity_id IN (
            SELECT entity_id FROM public.entity_users 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert clients for their agencies" ON public.agency_clients
    FOR INSERT WITH CHECK (
        agency_entity_id IN (
            SELECT entity_id FROM public.entity_users 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update clients for their agencies" ON public.agency_clients
    FOR UPDATE USING (
        agency_entity_id IN (
            SELECT entity_id FROM public.entity_users 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete clients for their agencies" ON public.agency_clients
    FOR DELETE USING (
        agency_entity_id IN (
            SELECT entity_id FROM public.entity_users 
            WHERE user_id = auth.uid()
        )
    );

-- Create RLS policies for agency_client_contacts
CREATE POLICY "Users can view contacts for their agency clients" ON public.agency_client_contacts
    FOR SELECT USING (
        client_id IN (
            SELECT id FROM public.agency_clients 
            WHERE agency_entity_id IN (
                SELECT entity_id FROM public.entity_users 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can insert contacts for their agency clients" ON public.agency_client_contacts
    FOR INSERT WITH CHECK (
        client_id IN (
            SELECT id FROM public.agency_clients 
            WHERE agency_entity_id IN (
                SELECT entity_id FROM public.entity_users 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can update contacts for their agency clients" ON public.agency_client_contacts
    FOR UPDATE USING (
        client_id IN (
            SELECT id FROM public.agency_clients 
            WHERE agency_entity_id IN (
                SELECT entity_id FROM public.entity_users 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can delete contacts for their agency clients" ON public.agency_client_contacts
    FOR DELETE USING (
        client_id IN (
            SELECT id FROM public.agency_clients 
            WHERE agency_entity_id IN (
                SELECT entity_id FROM public.entity_users 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create a view for clients with their contacts
CREATE OR REPLACE VIEW public.agency_clients_with_contacts AS
SELECT 
    c.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', cc.id,
                'name', cc.name,
                'email', cc.email,
                'phone', cc.phone,
                'position', cc.position,
                'is_primary', cc.is_primary,
                'created_at', cc.created_at,
                'updated_at', cc.updated_at
            ) ORDER BY cc.is_primary DESC, cc.name
        ) FILTER (WHERE cc.id IS NOT NULL),
        '[]'::json
    ) AS contacts
FROM public.agency_clients c
LEFT JOIN public.agency_client_contacts cc ON c.id = cc.client_id
GROUP BY c.id, c.agency_entity_id, c.name, c.type, c.email, c.phone, c.address, c.vat_number, c.notes, c.created_at, c.updated_at;
