import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/core';
import { supabase } from '@/core/api/supabase';
import { useAuth } from '@/contexts/AuthContext';

// Import form section components
import { BasicInformation, ContactManagement } from './form-sections';

// Import API services
import {
  createAgencyClient,
  updateAgencyClient,
  getAgencyClient,
  createClientContact,
  updateClientContact,
  deleteClientContact
} from '../api/clientService';
import type {
  AgencyClientWithContacts,
  CreateAgencyClientContactData,
  AgencyClientContact,
  EditableContactData
} from '../types';

interface ClientModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  client?: AgencyClientWithContacts | null; // For editing
  mode: 'create' | 'edit';
}

const ClientModal = ({ open, onClose, onSuccess, client, mode }: ClientModalProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();

  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<'company' | 'person'>('company');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [vatNumber, setVatNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [contacts, setContacts] = useState<EditableContactData[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingClient, setLoadingClient] = useState(false);
  const [agencyEntityId, setAgencyEntityId] = useState<string | null>(null);

  // Get agency entity ID
  useEffect(() => {
    const getAgencyEntityId = async () => {
      if (!profile?.id) return;

      try {
        const { data: agencyEntities, error } = await supabase
          .from('entity_users')
          .select('entity_id')
          .eq('user_id', profile.id);

        if (error) {
          console.error('Error fetching agency entities:', error);
          return;
        }

        if (agencyEntities && agencyEntities.length > 0) {
          setAgencyEntityId(agencyEntities[0].entity_id);
        }
      } catch (error) {
        console.error('Exception fetching agency entity:', error);
      }
    };

    if (open) {
      getAgencyEntityId();
    }
  }, [profile?.id, open]);

  // Initialize form with client data for editing
  useEffect(() => {
    const loadClientData = async () => {
      if (mode === 'edit' && client && open) {
        setLoadingClient(true);
        try {
          // Load full client data with contacts
          const fullClientData = await getAgencyClient(client.id);

          if (fullClientData) {
            setName(fullClientData.name);
            setType(fullClientData.type);
            setEmail(fullClientData.email || '');
            setPhone(fullClientData.phone || '');
            setAddress(fullClientData.address || '');
            setVatNumber(fullClientData.vat_number || '');
            setNotes(fullClientData.notes || '');

            // Convert existing contacts to editable format
            if (fullClientData.contacts && Array.isArray(fullClientData.contacts)) {
              const editableContacts: EditableContactData[] = fullClientData.contacts.map(contact => ({
                id: contact.id,
                name: contact.name,
                email: contact.email || '',
                phone: contact.phone || '',
                position: contact.position || '',
                is_primary: contact.is_primary,
                isNew: false,
                isDeleted: false
              }));
              setContacts(editableContacts);
            } else {
              setContacts([]);
            }
          }
        } catch (error) {
          console.error('Error loading client data:', error);
          toast({
            title: 'Error',
            description: 'Failed to load client data. Please try again.',
            variant: 'destructive',
          });
        } finally {
          setLoadingClient(false);
        }
      } else if (mode === 'create' || !open) {
        // Reset form for create mode or when modal closes
        resetForm();
      }
    };

    loadClientData();
  }, [mode, client?.id, open]);

  const resetForm = () => {
    setName('');
    setType('company');
    setEmail('');
    setPhone('');
    setAddress('');
    setVatNumber('');
    setNotes('');
    setContacts([]);
  };

  const validateForm = () => {
    if (!name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Client name is required.',
        variant: 'destructive',
      });
      return false;
    }

    if (!agencyEntityId) {
      toast({
        title: 'Error',
        description: 'Unable to determine agency. Please try again.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      if (mode === 'create') {
        // Create mode - handle as before
        const clientData = {
          agency_entity_id: agencyEntityId!,
          name: name.trim(),
          type,
          email: email.trim() || undefined,
          phone: phone.trim() || undefined,
          address: address.trim() || undefined,
          vat_number: vatNumber.trim() || undefined,
          notes: notes.trim() || undefined,
          contacts: type === 'company' ? contacts.map(c => ({
            name: c.name,
            email: c.email || undefined,
            phone: c.phone || undefined,
            position: c.position || undefined,
            is_primary: c.is_primary || false
          })) : undefined
        };

        await createAgencyClient(clientData);
        toast({
          title: 'Success',
          description: 'Client created successfully.',
        });
      } else if (mode === 'edit' && client) {
        // Update client basic information
        await updateAgencyClient(client.id, {
          name: name.trim(),
          type,
          email: email.trim() || null,
          phone: phone.trim() || null,
          address: address.trim() || null,
          vat_number: vatNumber.trim() || null,
          notes: notes.trim() || null
        });

        // Handle contact updates for company clients
        if (type === 'company') {
          // Process contact changes
          for (const contact of contacts) {
            if (contact.isDeleted && contact.id) {
              // Delete existing contact
              await deleteClientContact(contact.id);
            } else if (contact.isNew || !contact.id) {
              // Create new contact
              await createClientContact(client.id, {
                name: contact.name,
                email: contact.email || undefined,
                phone: contact.phone || undefined,
                position: contact.position || undefined,
                is_primary: contact.is_primary || false
              });
            } else if (contact.id) {
              // Update existing contact
              await updateClientContact(contact.id, {
                name: contact.name,
                email: contact.email || null,
                phone: contact.phone || null,
                position: contact.position || null,
                is_primary: contact.is_primary || false
              });
            }
          }
        }

        toast({
          title: 'Success',
          description: 'Client updated successfully.',
        });
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving client:', error);
      toast({
        title: 'Error',
        description: `Failed to ${mode} client. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      resetForm();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New Client' : 'Edit Client'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new client for your agency. You can add contacts for company clients.'
              : 'Update client information and manage contacts.'
            }
          </DialogDescription>
        </DialogHeader>

        {loadingClient ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading client data...</span>
          </div>
        ) : (
          <div className="grid gap-6 py-4">
            {/* Basic Information Section */}
            <BasicInformation
              name={name}
              setName={setName}
              type={type}
              setType={setType}
              email={email}
              setEmail={setEmail}
              phone={phone}
              setPhone={setPhone}
              address={address}
              setAddress={setAddress}
              vatNumber={vatNumber}
              setVatNumber={setVatNumber}
              notes={notes}
              setNotes={setNotes}
            />

            {/* Contact Management Section (only for companies) */}
            <ContactManagement
              clientType={type}
              contacts={contacts}
              setContacts={setContacts}
            />
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading || loadingClient}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || loadingClient || !name.trim()}
            className="bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {mode === 'create' ? 'Creating...' : 'Updating...'}
              </>
            ) : (
              mode === 'create' ? 'Create Client' : 'Update Client'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClientModal;
